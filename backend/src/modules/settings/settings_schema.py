from datetime import datetime
from typing import Optional

from pydantic import UUID4, BaseModel, Field, EmailStr, HttpUrl

from src.core.shared_schema import BaseRequest


class CountryFilter(BaseRequest):
	name: Optional[str] = Field(default=None, description="Filter by country name")


class RegionFilter(BaseRequest):
	name: Optional[str] = Field(default=None, description="Filter by country name")


class DistrictFilter(BaseRequest):
	name: Optional[str] = Field(default=None, description="Filter by country name")
	region_id: Optional[UUID4] = Field(default=None, description="Filter by region id")


class LoadableItemFilter(BaseRequest):
	code: Optional[str] = Field(default=None, description="Filter by loadable item name")
	type: Optional[str] = Field(default=None, description="Filter by loadable item type")
	display_value: Optional[str] = Field(default=None, description="Filter by loadable item name")


class RegionDto(BaseModel):
	id: UUID4
	name: str
	code: str
	created_at: datetime
	updated_at: datetime


class DistrictDto(BaseModel):
	id: UUID4
	name: str
	code: str
	region_id: UUID4
	created_at: datetime
	updated_at: datetime
	region_name: str


class LoadableItemDto(BaseModel):
	id: UUID4
	type: str
	code: str
	display_value: str
	description: Optional[str] = None
	created_at: datetime
	updated_at: datetime


class LoadableItemRequest(BaseModel):
	id: Optional[str] = Field(default=None, description="Id")
	type: str
	code: str
	display_value: str
	description: Optional[str] = Field(default=None, description="Description")


class CountryDto(BaseModel):
	id: UUID4
	name: str
	dial_code: str
	short_code: str
	flag: str


class SystemConfigurationRequest(BaseModel):
	system_title: str = Field(..., min_length=1, max_length=200, description="System title")
	short_name: str = Field(..., min_length=1, max_length=50, description="Short name")
	telephone: str = Field(..., min_length=1, max_length=50, description="Telephone number")
	website: HttpUrl = Field(..., description="Website URL")
	address: str = Field(..., min_length=1, max_length=500, description="Address")
	support_contact: str = Field(..., min_length=1, max_length=200, description="Support contact")
	support_telephone: str = Field(..., min_length=1, max_length=50, description="Support telephone")
	licensee: str = Field(..., min_length=1, max_length=200, description="Licensee")
	slogan: str = Field(..., min_length=1, max_length=300, description="Slogan")
	email: EmailStr = Field(..., description="Email address")
	support_email: EmailStr = Field(..., description="Support email address")
	landing_page: str = Field(..., min_length=1, max_length=100, description="Landing page")


class SystemConfigurationDto(BaseModel):
	system_title: Optional[str] = None
	short_name: Optional[str] = None
	telephone: Optional[str] = None
	website: Optional[str] = None
	address: Optional[str] = None
	support_contact: Optional[str] = None
	support_telephone: Optional[str] = None
	licensee: Optional[str] = None
	slogan: Optional[str] = None
	email: Optional[str] = None
	support_email: Optional[str] = None
	landing_page: Optional[str] = None
	updated_at: Optional[datetime] = None
