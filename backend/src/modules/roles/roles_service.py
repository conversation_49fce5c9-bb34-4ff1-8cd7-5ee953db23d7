from typing import List
from uuid import UUID

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate

from src.core.shared_schema import Pagination
from src.config.db.models.loadable_item import LoadableItem
from src.config.db.models.role import Role
from src.config.db.models.role_permission import RolePermission
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.modules.roles.roles_schema import (
	RoleCreate,
	RoleFilters,
	RolePermissionAssignRequest,
	RoleResponse,
	RoleUpdate,
)


class RolesService(BaseRepository):
	def __init__(self):
		super().__init__()

	async def find_roles(self, filters: RoleFilters) -> Pagination[RoleResponse]:
		"""
		Find roles based on filters.

		Args:
		    filters (RoleFilters): Filters to apply.

		Returns:
		    Query: SQLAlchemy query object for roles matching the criteria.
		"""
		role_filters = ["code", "name", "description"]

		try:
			rfilters: dict = {k: v for k, v in filters if v is not None and k in role_filters}

			query = self.db.query(Role)

			if rfilters:
				# Handle partial matching for name and description
				for field in ["name", "description"]:
					if field in rfilters:
						filter_value = rfilters.pop(field)
						query = query.filter(getattr(Role, field).like(f"%{filter_value}%"))

				# Handle exact matching for remaining filters
				query = query.filter_by(**rfilters)
    
			roles = paginate(query, Params(page=filters.page, size=filters.size))
			data = [row for row in roles.items]

			return Pagination.from_query_result(data, roles)
		except Exception as e:
			self.logger.error(f"Error finding roles: {e}")
			raise ApiException("Failed to retrieve roles")

	def create_role(self, role_data: RoleCreate) -> RoleResponse:
		"""
		Create a new role.

		Args:
		    role_data (RoleCreate): Role data to create.

		Returns:
		    RoleResponse: Created role data.
		"""
		try:
			# Check if role with same code already exists
			existing_role = self.db.query(Role).filter(Role.code == role_data.code).first()

			if existing_role:
				raise ApiException("Role with this code already exists")

			role = Role(code=role_data.code, name=role_data.name, description=role_data.description)

			self.db.add(role)
			self.db.commit()
			self.db.refresh(role)

			return role
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error creating role: {e}")
			raise ApiException("Failed to create role")

	def get_role_by_id(self, role_id: UUID) -> RoleResponse:
		"""
		Get a role by ID.

		Args:
		    role_id (UUID): Role ID.

		Returns:
		    RoleResponse: Role data.
		"""
		try:
			role = self.db.query(Role).filter(Role.id == role_id).first()

			if not role:
				raise ApiException("Role not found")

			return self._build_role_response(role)
		except Exception as e:
			self.logger.error(f"Error getting role: {e}")
			raise ApiException("Failed to retrieve role")

	def update_role(self, role_id: UUID, updates: RoleUpdate) -> RoleResponse:
		"""
		Update a role.

		Args:
		    role_id (UUID): Role ID to update.
		    updates (RoleUpdate): Updates to apply.

		Returns:
		    RoleResponse: Updated role data.
		"""
		try:
			role = self.db.query(Role).filter(Role.id == role_id).first()

			if not role:
				raise ApiException("Role not found")

			# Check for conflicts with code if it's being updated
			update_dict = {k: v for k, v in updates if v is not None}

			if "code" in update_dict:
				existing_code = self.db.query(Role).filter(Role.id != role_id, Role.code == update_dict["code"]).first()
				if existing_code:
					raise ApiException("Role with this code already exists")

			# Apply updates
			for k, v in update_dict.items():
				setattr(role, k, v)

			self.db.commit()
			self.db.refresh(role)

			return role
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error updating role: {e}")
			raise ApiException("Failed to update role")

	def delete_role(self, role_id: UUID, delete_reason: str) -> None:
		"""
		Soft delete a role by setting voided=True.

		Args:
		    role_id (UUID): Role ID to delete.
		    delete_reason (str): Reason for deletion.
		"""
		try:
			role = self.db.query(Role).filter(Role.id == role_id).first()

			if not role:
				raise ApiException("Role not found")

			# Check if role has active users
			if role.user_roles:
				# Check if there are any non-voided user roles
				from src.config.db.models.user_role import UserRole

				active_user_roles = (
					self.db.query(UserRole).filter(UserRole.role_id == role_id, ~UserRole.voided).first()
				)

				if active_user_roles:
					raise ApiException("Cannot delete role with active users. Please reassign users first.")

			role.voided = True
			role.voided_reason = delete_reason
			self.db.commit()

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error deleting role: {e}")
			raise ApiException("Failed to delete role")

	def assign_permissions_to_role(self, request: RolePermissionAssignRequest) -> RoleResponse:
		"""
		Assign multiple permissions to a role.

		Args:
		    request (RolePermissionAssignRequest): Assignment request data.

		Returns:
		    RolePermissionsResponse: Assignment response data.
		"""
		try:
			# Validate role exists
			role = self.db.query(Role).filter(Role.id == request.role_id).first()
			if not role:
				raise ApiException("Role not found")

			# Validate all permissions exist and are of type PERMISSION
			permissions = (
				self.db.query(LoadableItem)
				.filter(LoadableItem.id.in_(request.permission_ids), LoadableItem.type == "PERMISSION")
				.all()
			)

			if len(permissions) != len(request.permission_ids):
				found_ids = {permission.id for permission in permissions}
				missing_ids = set(request.permission_ids) - found_ids
				raise ApiException(f"Permissions not found: {missing_ids}")

			for permission_id in request.permission_ids:
				# Check if role already has this permission assignment
				existing_assignment = (
					self.db.query(RolePermission)
					.filter(RolePermission.role_id == request.role_id, RolePermission.permission_id == permission_id)
					.first()
				)

				if existing_assignment:
					# Skip if already assigned
					continue

				# Create new assignment
				assignment = RolePermission(role_id=request.role_id, permission_id=permission_id)

				self.db.add(assignment)

			self.db.commit()

			return role

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error assigning permissions to role: {e}")
			raise ApiException("Failed to assign permissions to role")

	def unassign_permissions_from_role(self, role_id: UUID, permission_ids: List[UUID] = None) -> None:
		"""
		Unassign permissions from a role. If permission_ids is None, unassign all permissions.

		Args:
		    role_id (UUID): Role ID to unassign permissions from.
		    permission_ids (List[UUID], optional): Specific permission IDs to unassign.
		                                          If None, unassign all permissions.
		"""
		try:
			# Build query for assignments to unassign
			query = self.db.query(RolePermission).filter(RolePermission.role_id == role_id, ~RolePermission.voided)

			if permission_ids:
				query = query.filter(RolePermission.permission_id.in_(permission_ids))

			assignments = query.all()

			if not assignments:
				if permission_ids:
					raise ApiException("Role does not have the specified permissions assigned")
				else:
					raise ApiException("Role does not have any permissions assigned")

			# Soft delete the assignments
			for assignment in assignments:
				assignment.voided = True
				assignment.voided_reason = "Permission unassigned from role"

			self.db.commit()

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error unassigning permissions from role: {e}")

			raise ApiException("Failed to unassign permissions from role")
