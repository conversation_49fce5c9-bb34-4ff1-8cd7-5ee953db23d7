'use client';

import React, { useState } from 'react';
import { createContext, useContext } from 'react';

type LoadingContextType = {
  loadingComponent: React.ReactNode;
  loading: boolean;
  message: string;
  showLoading: (msg?: string, loadingComponent?: React.ReactNode) => void;
  hideLoading: () => void;
};

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export default function LoadingProvider({ children }: { children: React.ReactNode }) {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('Loading...');
  const [loadingComponent, setLoadingComponent] = useState<React.ReactNode | null>(null);

  const showLoading = (msg?: string, loadingComponent?: React.ReactNode) => {
    if(msg) setMessage(msg);
    if(loadingComponent) setLoadingComponent(loadingComponent)
    setLoading(true);
  };

  const hideLoading = () => {
    setLoading(false);
  };

  return (
    <LoadingContext.Provider value={{loadingComponent, loading, message, showLoading, hideLoading }}>
      {children}
    </LoadingContext.Provider>
  );
}

export function useLoading() {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};