"use client";

import { createContext, useContext, useState } from "react";

type ErrorContextType = {
    showError: (message: string, retryCallback?: () => void) => void;
    hideError: () => void;
    errorMessage: string | null;
    onRetry: () => void;
};

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

export function ErrorProvider({ children }: { children: React.ReactNode }) {
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [onRetry, setOnRetry] = useState<() => void>(() => () => window.location.reload());

    const showError = (message: string, retryCallback?: () => void) => {
        setErrorMessage(message);
        if (retryCallback) setOnRetry(() => retryCallback);
    };
    const hideError = () => setErrorMessage(null);

    return (
        <ErrorContext.Provider value={{ showError, hideError, errorMessage, onRetry }}>
            {children}
        </ErrorContext.Provider>
    );
}

export function useError() {
    const context = useContext(ErrorContext);

    if (!context) throw new Error("useError must be used within ErrorProvider");

    return context;
}
