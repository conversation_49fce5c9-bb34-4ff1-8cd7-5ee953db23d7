import api from "@/config/api.config";
import http from "@/config/http";
import {
    HttpResponse,
    TemplateDto,
    TemplateFilter,
    TemplateRequest,
    TemplateStageDto,
    TemplateStageFilter,
    TemplateStageRequest,
    TemplateStageRoleDto,
    TemplateStageRoleFilter,
    TemplateStageRoleRequest,
    TemplateStageTriggerDto,
    TemplateStageTriggerFilter,
    TemplateStageTriggerRequest,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

// Template operations
export const fetchTemplates = async (filter: Partial<TemplateFilter> = {}): Promise<HttpResponse<TemplateDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.workflows.templates.root}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<TemplateDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getTemplateById = async (id: string): Promise<HttpResponse<TemplateDto>> => {
    try {
        const response = await http.get(api.v1.workflows.templates.action(id));

        return httpResponse<TemplateDto>({ data: response });
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createTemplate = async (data: TemplateRequest): Promise<HttpResponse<TemplateDto>> => {
    try {
        const response = await http.post(api.v1.workflows.templates.root, data);

        return httpResponse<TemplateDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateTemplate = async (id: string, data: TemplateRequest): Promise<HttpResponse<TemplateDto>> => {
    try {
        const response = await http.put(api.v1.workflows.templates.action(id), data);

        return httpResponse<TemplateDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const voidTemplate = async (id: string, voidReason: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.workflows.templates.void(id), {
            void_reason: voidReason,
        });

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Template Stage operations
export const fetchTemplateStages = async (
    templateId: string,
    filter: Partial<TemplateStageFilter> = {},
): Promise<HttpResponse<TemplateStageDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.workflows.templates.stages.root(templateId)}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<TemplateStageDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createTemplateStage = async (
    templateId: string,
    data: TemplateStageRequest,
): Promise<HttpResponse<TemplateStageDto[]>> => {
    try {
        const response = await http.post(api.v1.workflows.templates.stages.root(templateId), data);

        return httpResponse<TemplateStageDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateTemplateStage = async (
    templateId: string,
    stageId: string,
    data: TemplateStageRequest,
): Promise<HttpResponse<TemplateStageDto>> => {
    try {
        const response = await http.put(api.v1.workflows.templates.stages.action(templateId, stageId), data);

        return httpResponse<TemplateStageDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const voidTemplateStage = async (
    templateId: string,
    stageId: string,
    voidReason: string,
): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.workflows.templates.stages.action(templateId, stageId), {
            data: {
                void_reason: voidReason,
            },
        });

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Template Stage Role operations
export const fetchTemplateStageRoles = async (
    templateId: string,
    stageId: string,
    filter: Partial<TemplateStageRoleFilter> = {},
): Promise<HttpResponse<TemplateStageRoleDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.workflows.templates.stages.roles.root(templateId, stageId)}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<TemplateStageRoleDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createTemplateStageRoles = async (
    templateId: string,
    stageId: string,
    data: TemplateStageRoleRequest[],
): Promise<HttpResponse<TemplateStageRoleDto[]>> => {
    try {
        const response = await http.post(api.v1.workflows.templates.stages.roles.root(templateId, stageId), data);

        return httpResponse<TemplateStageRoleDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const voidTemplateStageRole = async (
    templateId: string,
    stageId: string,
    roleId: string,
): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.workflows.templates.stages.roles.action(templateId, stageId, roleId));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Template Stage Trigger operations
export const fetchTemplateStageTriggers = async (
    templateId: string,
    stageId: string,
    filter: Partial<TemplateStageTriggerFilter> = {},
): Promise<HttpResponse<TemplateStageTriggerDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.workflows.templates.stages.triggers.root(templateId, stageId)}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<TemplateStageTriggerDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createTemplateStageTriggers = async (
    templateId: string,
    stageId: string,
    data: TemplateStageTriggerRequest[],
): Promise<HttpResponse<TemplateStageTriggerDto[]>> => {
    try {
        const response = await http.post(api.v1.workflows.templates.stages.triggers.root(templateId, stageId), data);

        return httpResponse<TemplateStageTriggerDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const voidTemplateStageTrigger = async (
    templateId: string,
    stageId: string,
    triggerId: string,
): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(
            api.v1.workflows.templates.stages.triggers.action(templateId, stageId, triggerId),
        );

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
