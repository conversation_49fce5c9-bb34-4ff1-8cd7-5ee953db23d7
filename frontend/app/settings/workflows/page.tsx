"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";

import { Button } from "@/components/ui/button";
import { WorkflowTemplatesList } from "@/components/workflows/WorkflowTemplatesList";
import { CreateTemplateModal } from "@/components/workflows/CreateTemplateModal";
import { WorkflowTemplateCode } from "@/types";

export default function WorkflowSettingsPage() {
    const router = useRouter();
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [selectedTemplateType, setSelectedTemplateType] = useState<WorkflowTemplateCode | null>(null);

    const handleTemplateCreated = () => {
        setIsCreateModalOpen(false);
        setSelectedTemplateType(null);
        // Refresh the templates list
    };

    return (
        <div className="flex flex-col space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <Button
                        className="flex items-center space-x-2"
                        size="sm"
                        variant="ghost"
                        onClick={() => router.push("/dashboard")}
                    >
                        <ArrowLeft className="h-4 w-4" />
                        <span>Back to Dashboard</span>
                    </Button>
                </div>
            </div>

            <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tight">Workflow Management</h1>
                <p className="text-muted-foreground">Configure approval workflows for different business processes</p>
            </div>

            <WorkflowTemplatesList />

            {/* Create Template Modal */}
            <CreateTemplateModal
                isOpen={isCreateModalOpen}
                templateType={selectedTemplateType}
                onClose={() => setIsCreateModalOpen(false)}
                onTemplateCreated={handleTemplateCreated}
            />
        </div>
    );
}
