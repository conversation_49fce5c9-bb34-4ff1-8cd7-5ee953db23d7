"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { ArrowLeft, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import * as workflowService from "@/services/WorkflowService";
import { TemplateRequest } from "@/types";

const templateSchema = z.object({
    name: z.string().min(5, "Name must be at least 5 characters"),
    code: z.string().min(3, "Code must be at least 3 characters"),
    description: z.string().optional(),
    is_active: z.boolean().default(true),
});

type TemplateFormData = z.infer<typeof templateSchema>;

export default function EditTemplatePage() {
    const params = useParams();
    const router = useRouter();
    const templateId = params.templateId as string;
    const [isSubmitting, setIsSubmitting] = useState(false);

    const form = useForm<TemplateFormData>({
        resolver: zodResolver(templateSchema),
        defaultValues: {
            name: "",
            code: "",
            description: "",
            is_active: true,
        },
    });

    // Fetch template details
    const {
        data: template,
        isLoading,
        error,
    } = useQuery({
        queryKey: ["workflow-template", templateId],
        queryFn: async () => {
            if (!templateId) return null;

            const response = await workflowService.getTemplateById(templateId);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return null;
            }

            return response.data || null;
        },
        enabled: !!templateId,
    });

    // Update form when template data is loaded
    useEffect(() => {
        if (template) {
            form.reset({
                name: template.name,
                code: template.code,
                description: template.description || "",
                is_active: template.is_active,
            });
        }
    }, [template, form]);

    const onSubmit = async (data: TemplateFormData) => {
        setIsSubmitting(true);

        try {
            const templateData: TemplateRequest = {
                name: data.name,
                code: data.code,
                description: data.description,
                is_active: data.is_active
            };

            const response = await workflowService.updateTemplate(templateId, templateData);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Workflow template updated successfully");
            router.push(`/settings/workflows/templates/${templateId}`);
        } catch {
            toast.error("Failed to update workflow template");
        } finally {
            setIsSubmitting(false);
        }
    };

    if (isLoading) {
        return (
            <div className="flex flex-col space-y-6 p-6">
                <div className="flex items-center space-x-4">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-32" />
                </div>
                <div className="space-y-2">
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-4 w-96" />
                </div>
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-48" />
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-20 w-full" />
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (error || !template) {
        return (
            <div className="flex flex-col space-y-6 p-6">
                <Alert>
                    <AlertDescription>Failed to load template details. Please try again.</AlertDescription>
                </Alert>
            </div>
        );
    }

    return (
        <div className="flex flex-col space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <Button
                        className="flex items-center space-x-2"
                        size="sm"
                        variant="ghost"
                        onClick={() => router.push(`/settings/workflows/templates/${templateId}`)}
                    >
                        <ArrowLeft className="h-4 w-4" />
                        <span>Back to Template</span>
                    </Button>
                </div>
            </div>

            {/* Page Title */}
            <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tight">Edit Workflow Template</h1>
                <p className="text-muted-foreground">Update the details for this workflow template</p>
            </div>

            {/* Edit Form */}
            <Card>
                <CardHeader>
                    <CardTitle>Template Details</CardTitle>
                    <CardDescription>Modify the basic information for this workflow template</CardDescription>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Template Name</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="Enter template name"
                                                    {...field}
                                                    disabled={isSubmitting}
                                                />
                                            </FormControl>
                                            <FormDescription>
                                                A descriptive name for this workflow template
                                            </FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="code"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Template Code</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="Enter template code"
                                                    {...field}
                                                    className="uppercase"
                                                    disabled={true}
                                                    onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                                                />
                                            </FormControl>
                                            <FormDescription>
                                                A unique code identifier for this template
                                            </FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                placeholder="Enter template description"
                                                {...field}
                                                disabled={isSubmitting}
                                                rows={4}
                                            />
                                        </FormControl>
                                        <FormDescription>
                                            Optional description of what this workflow template is used for
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="is_active"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Active Template</FormLabel>
                                            <FormDescription>Enable this template for use in workflows</FormDescription>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                disabled={isSubmitting}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <div className="flex justify-end space-x-2 pt-4">
                                <Button
                                    disabled={isSubmitting}
                                    type="button"
                                    variant="outline"
                                    onClick={() => router.push(`/settings/workflows/templates/${templateId}`)}
                                >
                                    Cancel
                                </Button>
                                <Button disabled={isSubmitting} type="submit">
                                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    Update Template
                                </Button>
                            </div>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </div>
    );
}
