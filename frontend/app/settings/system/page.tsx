"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { SystemConfigurationForm } from "@/components/forms/SystemConfigurationForm";
import { useSystemConfiguration } from "@/hooks/use-system-configuration";
import { useError } from '@/context/ErrorContext';
import { toast } from 'sonner';
import { useLoading } from '@/context/LoadingContext';

interface SystemConfigurationData {
  system_title: string;
  short_name: string;
  telephone: string;
  website: string;
  address: string;
  support_contact: string;
  support_telephone: string;
  licensee: string;
  slogan: string;
  email: string;
  support_email: string;
  landing_page: string;
}

export default function SystemConfigurationPage() {
  const router = useRouter();
  const { loading, updating, error, data, loadConfiguration, updateConfiguration } = useSystemConfiguration();
  const { showError, hideError } = useError()
  const { showLoading, hideLoading } = useLoading()

  // Load existing configuration on component mount
  useEffect(() => {
    loadConfiguration();
  }, [loadConfiguration]);

  useEffect(() => {
    if(loading) showLoading('Loading system configuration...')
    if (updating) showLoading('Updating system configuration...')

    !loading && !updating ? hideLoading() : null
  }, [loading, updating]);

  useEffect(() => {
    error ? showError('Failed to load system configuration', loadConfiguration) : hideError();
  }, [error]);

  const handleSubmit = async (formData: SystemConfigurationData) => {
    await updateConfiguration(formData);
    toast.success('System configuration updated successfully!');
  };

  const handleCancel = () => {
    router.push("/settings");
  };

  return (
    <div className="container mx-auto py-6">
      <SystemConfigurationForm
        initialData={data || {}}
        loading={loading || updating}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
}
