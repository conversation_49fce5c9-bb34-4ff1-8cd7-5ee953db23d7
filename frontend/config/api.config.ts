const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

const v1 = `${baseUrl}/v1`;
const roots = {
    auth: v1 + "/auth",
    roles: v1 + "/roles",
    settings: v1 + "/settings",
    users: v1 + "/users",
    workflows: v1 + "/workflows",
    organizations: v1 + "/organizations",
    departments: v1 + "/departments",
    complaints: v1 + "/complaints",
    activities: v1 + "/activities",
    applications: v1 + "/applications",
    financial: v1 + "/financials",
};

const settings = {
    root: roots.settings,
    countries: roots.settings + "/countries",
    districts: roots.settings + "/districts",
    regions: roots.settings + "/regions",
    system_configuration: roots.settings + "/system-configuration",
    loadable_items: {
        root: roots.settings + "/loadable_items",
        action: (id: string) => roots.settings + "/loadable_items/" + id,
    },
};

const departments = {
    root: roots.departments,
    action: (id: string) => roots.departments + "/" + id,
};

const complaints = {
    root: roots.complaints,
    action: (id: string) => roots.complaints + "/" + id,
};

const activities = {
    root: roots.departments,
    action: (id: string) => roots.activities + "/" + id,
};

const organizations = {
    root: roots.organizations,
    action: (id: string) => roots.organizations + "/" + id,
};

const financial = {
    root: roots.financial,
    currencies: roots.financial + "/currencies",
    fees: {
        root: roots.financial + "/fees",
        action: (id: string) => roots.financial + "/fees/" + id,
        schedule: (id: string) => roots.financial + "/fees/" + id + "/schedule",
        activate: (id: string) => roots.financial + "/fees/" + id + "/activate",
        history: (categoryId: string) => roots.financial + "/fees/history/" + categoryId,
    },
    invoices: {
        root: roots.financial + "/invoices",
        action: (id: string) => roots.financial + "/invoices/" + id,
    },
};

const auth = {
    login: v1 + "/auth/login",
    refreshToken: v1 + "/auth/refresh-token",
    register: v1 + "/auth/register",
    logout: v1 + "/auth/logout",
    resendAccountVerification: v1 + "/auth/resend-account-verification",
    requestPasswordReset: v1 + "/auth/request-password-reset",
    resetPassword: v1 + "/auth/reset-password",
    two_factor: v1 + "/auth/2fa",
};

const api = {
    v1: {
        auth,
        roles: {
            root: v1 + "/roles",
            delete: (id: string) => `${v1}/roles/${id}`,
        },
        settings,
        organizations,
        departments,
        complaints,
        activities,
        financial,
        users: {
            root: v1 + "/users",
            delete: (id: string) => `${v1}/users/${id}`,
        },
        workflows: {
            root: v1 + "/workflows",
            delete: (id: string) => `${v1}/workflows/${id}`,
            templates: {
                root: v1 + "/workflows/templates",
                action: (id: string) => `${v1}/workflows/templates/${id}`,
                void: (id: string) => `${v1}/workflows/templates/${id}/void`,
                stages: {
                    root: (templateId: string) => `${v1}/workflows/templates/${templateId}/stages`,
                    action: (templateId: string, stageId: string) => `${v1}/workflows/templates/${templateId}/stages/${stageId}`,
                    roles: {
                        root: (templateId: string, stageId: string) => `${v1}/workflows/templates/${templateId}/stages/${stageId}/roles`,
                        action: (templateId: string, stageId: string, roleId: string) => `${v1}/workflows/templates/${templateId}/stages/${stageId}/roles/${roleId}`,
                    },
                    triggers: {
                        root: (templateId: string, stageId: string) => `${v1}/workflows/templates/${templateId}/stages/${stageId}/triggers`,
                        action: (templateId: string, stageId: string, triggerId: string) => `${v1}/workflows/templates/${templateId}/stages/${stageId}/triggers/${triggerId}`,
                    },
                },
            },
        },
    },
};

export default api;
