'use client';

import { ReactNode } from 'react';

import { ErrorProvider } from '@/context/ErrorContext';
import LoadingProvider from '@/context/LoadingContext';
import { QueryProvider } from '@/app/providers';
import ErrorDialog from '@/components/ui/error-dialog';
import LoadingDialog from '@/components/loading-dialog';

export function Providers({ children }: { children: ReactNode }) {
    return (

        <QueryProvider>
            <ErrorProvider>
                <ErrorDialog />
                <LoadingProvider>
                    <LoadingDialog />
                    {children}
                </LoadingProvider>
            </ErrorProvider>
        </QueryProvider>
    );
}
