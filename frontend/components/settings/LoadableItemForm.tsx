"use client";

import React, { useState, useEffect } from "react";
import { X, Save, Loader2, Package } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@/components/ui/select";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadableItemDto } from "@/types";

interface LoadableItemFormData {
    type: string;
    code: string;
    display_value: string;
    description: string;
}

interface LoadableItemFormProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (data: LoadableItemFormData) => Promise<void>;
    initialData?: LoadableItemDto | null;
    availableTypes: string[];
    loading?: boolean;
    mode: "create" | "edit";
}

export const LoadableItemForm: React.FC<LoadableItemFormProps> = ({
    open,
    onClose,
    onSubmit,
    initialData,
    availableTypes,
    loading = false,
    mode,
}) => {
    const [formData, setFormData] = useState<LoadableItemFormData>({
        type: "",
        code: "",
        display_value: "",
        description: "",
    });
    const [errors, setErrors] = useState<Partial<LoadableItemFormData>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        if (initialData && mode === "edit") {
            setFormData({
                type: initialData.type,
                code: initialData.code,
                display_value: initialData.display_value,
                description: initialData.description || "",
            });
        } else {
            setFormData({
                type: "",
                code: "",
                display_value: "",
                description: "",
            });
        }
        setErrors({});
    }, [initialData, mode, open]);

    const validateForm = (): boolean => {
        const newErrors: Partial<LoadableItemFormData> = {};

        if (!formData.type.trim()) {
            newErrors.type = "Type is required";
        }

        if (!formData.code.trim()) {
            newErrors.code = "Code is required";
        } else if (formData.code.length < 2) {
            newErrors.code = "Code must be at least 2 characters";
        }

        if (!formData.display_value.trim()) {
            newErrors.display_value = "Display value is required";
        } else if (formData.display_value.length < 2) {
            newErrors.display_value = "Display value must be at least 2 characters";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (field: keyof LoadableItemFormData, value: string) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: undefined }));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setIsSubmitting(true);
        try {
            await onSubmit(formData);
            onClose();
        } catch (error) {
            console.error("Form submission error:", error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const typeOptions = availableTypes.map(type => ({
        value: type,
        label: type.charAt(0) + type.slice(1).toLowerCase(),
    }));

    // Add option for custom type
    typeOptions.push({ value: "CUSTOM", label: "Custom Type" });

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        {mode === "create" ? "Create Loadable Item" : "Edit Loadable Item"}
                    </DialogTitle>
                    <DialogDescription>
                        {mode === "create" 
                            ? "Add a new loadable item to the system"
                            : "Update the loadable item information"
                        }
                    </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Item Details</CardTitle>
                            <CardDescription>
                                Configure the basic information for this loadable item
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Select
                                        label="Type *"
                                        placeholder="Select type"
                                        options={typeOptions}
                                        value={formData.type}
                                        onChange={(value) => handleInputChange("type", value)}
                                        error={errors.type}
                                        disabled={loading || isSubmitting}
                                    />
                                </div>
                                <div>
                                    <Input
                                        label="Code *"
                                        placeholder="Enter unique code"
                                        value={formData.code}
                                        onChange={(e) => handleInputChange("code", e.target.value.toUpperCase())}
                                        error={errors.code}
                                        disabled={loading || isSubmitting}
                                    />
                                </div>
                            </div>

                            <div>
                                <Input
                                    label="Display Value *"
                                    placeholder="Enter display name"
                                    value={formData.display_value}
                                    onChange={(e) => handleInputChange("display_value", e.target.value)}
                                    error={errors.display_value}
                                    disabled={loading || isSubmitting}
                                />
                            </div>

                            <div>
                                <Textarea
                                    label="Description"
                                    placeholder="Enter description (optional)"
                                    value={formData.description}
                                    onChange={(e) => handleInputChange("description", e.target.value)}
                                    error={errors.description}
                                    disabled={loading || isSubmitting}
                                    rows={3}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end gap-3">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={isSubmitting}
                        >
                            <X className="mr-2 h-4 w-4" />
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting || loading}
                        >
                            {isSubmitting ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                <Save className="mr-2 h-4 w-4" />
                            )}
                            {mode === "create" ? "Create Item" : "Update Item"}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
};
