'use client';

import { LucideHourglass } from 'lucide-react';
import { useLoading } from '../context/LoadingContext';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from './ui/dialog';

export default function LoadingDialog() {
  const { loading, message, loadingComponent } = useLoading();

  if (!loading) return null;

  return (
    // TODO: Improve the loading animation
    <Dialog open={loading}>
      <DialogContent>
        <DialogTitle>Please wait ...</DialogTitle>
        <DialogDescription className="mb-4 text-center flex items-center justify-center py-6">
          {loadingComponent ? loadingComponent : (
            <>
              <LucideHourglass className="h-6 w-6 animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">{message}</span>
            </>
          )
          }
        </DialogDescription>

      </DialogContent>
    </Dialog>
  )
}