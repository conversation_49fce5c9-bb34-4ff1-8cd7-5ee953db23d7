"use client";

import React, { useState, useEffect } from "react";
import { Settings, Building2, Phone, User, MessageSquare } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface SystemConfigurationData {
  system_title: string;
  short_name: string;
  telephone: string;
  website: string;
  address: string;
  support_contact: string;
  support_telephone: string;
  licensee: string;
  slogan: string;
  email: string;
  support_email: string;
  landing_page: string;
}

interface SystemConfigurationFormProps {
  initialData?: Partial<SystemConfigurationData>;
  loading?: boolean;
  onSubmit?: (data: SystemConfigurationData) => Promise<void>;
  onCancel?: () => void;
}

const landingPageOptions = [
  { value: "Dashboard", label: "Dashboard" },
  { value: "Organizations", label: "Organizations" },
  { value: "Activities", label: "Activities" },
  { value: "Reports", label: "Reports" },
];

export const SystemConfigurationForm: React.FC<SystemConfigurationFormProps> = ({
  initialData = {},
  loading = false,
  onSubmit,
  onCancel,
}) => {
  const [formData, setFormData] = useState<SystemConfigurationData>({
    system_title: initialData?.system_title || "",
    short_name: initialData?.short_name || "",
    telephone: initialData?.telephone || "",
    website: initialData?.website || "",
    address: initialData?.address || "",
    support_contact: initialData?.support_contact || "",
    support_telephone: initialData?.support_telephone || "",
    licensee: initialData?.licensee || "",
    slogan: initialData?.slogan || "",
    email: initialData?.email || "",
    support_email: initialData?.support_email || "",
    landing_page: initialData?.landing_page || landingPageOptions[0].value,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData]);

  const handleInputChange = (field: keyof SystemConfigurationData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.system_title.trim()) newErrors.system_title = "System title is required";
    if (!formData.short_name.trim()) newErrors.short_name = "Short name is required";
    if (!formData.telephone.trim()) newErrors.telephone = "Telephone is required";
    if (!formData.website.trim()) newErrors.website = "Website is required";
    if (!formData.address.trim()) newErrors.address = "Address is required";
    if (!formData.support_contact.trim()) newErrors.support_contact = "Support contact is required";
    if (!formData.support_telephone.trim()) newErrors.support_telephone = "Support telephone is required";
    if (!formData.licensee.trim()) newErrors.licensee = "Licensee is required";
    if (!formData.slogan.trim()) newErrors.slogan = "Slogan is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!formData.support_email.trim()) newErrors.support_email = "Support email is required";
    if (!formData.landing_page.trim()) newErrors.landing_page = "Landing page is required";

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    if (formData.support_email && !emailRegex.test(formData.support_email)) {
      newErrors.support_email = "Please enter a valid support email address";
    }

    // Website validation
    const urlRegex = /^https?:\/\/.+/;
    if (formData.website && !urlRegex.test(formData.website)) {
      newErrors.website = "Website must start with http:// or https://";
    }

    // Phone validation (basic)
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
    if (formData.telephone && !phoneRegex.test(formData.telephone)) {
      newErrors.telephone = "Please enter a valid telephone number";
    }
    if (formData.support_telephone && !phoneRegex.test(formData.support_telephone)) {
      newErrors.support_telephone = "Please enter a valid support telephone number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsSubmitting(true);
    await onSubmit?.(formData);
    setIsSubmitting(false);
  };

  return (
    <div className="max-w-full mx-auto space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-primary/10 rounded-lg">
          <Settings className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">System Configuration</h1>
          <p className="text-muted-foreground">Configure your system settings and preferences</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Configure the basic system information and branding
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Input
                  label="System Title *"
                  placeholder="myNGO"
                  value={formData.system_title || ''}
                  onChange={(e) => handleInputChange("system_title", e.target.value)}
                  error={errors.system_title}
                  disabled={loading || isSubmitting}
                />
              </div>
              <div>
                <Input
                  label="Short Name *"
                  placeholder="NGORA"
                  value={formData.short_name || ''}
                  onChange={(e) => handleInputChange("short_name", e.target.value)}
                  error={errors.short_name}
                  disabled={loading || isSubmitting}
                />
              </div>
            </div>
            
            <div>
              <Input
                label="Licensee *"
                placeholder=""
                value={formData.licensee || ''}
                onChange={(e) => handleInputChange("licensee", e.target.value)}
                error={errors.licensee}
                disabled={loading || isSubmitting}
              />
            </div>

            <div>
              <Input
                label="Slogan *"
                placeholder=""
                value={formData.slogan || ''}
                onChange={(e) => handleInputChange("slogan", e.target.value)}
                error={errors.slogan}
                disabled={loading || isSubmitting}
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Contact Information
            </CardTitle>
            <CardDescription>
              Configure contact details and communication channels
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Input
                  label="Telephone *"
                  placeholder="+265 990 111 222"
                  value={formData.telephone || ''}
                  onChange={(e) => handleInputChange("telephone", e.target.value)}
                  error={errors.telephone}
                  disabled={loading || isSubmitting}
                />
              </div>
              <div>
                <Input
                  label="Email *"
                  type="email"
                  placeholder="email@..."
                  value={formData.email || ''}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  error={errors.email}
                  disabled={loading || isSubmitting}
                />
              </div>
            </div>

            <div>
              <Input
                label="Website *"
                placeholder="http(s)://..."
                value={formData.website || ''}
                onChange={(e) => handleInputChange("website", e.target.value)}
                error={errors.website}
                disabled={loading || isSubmitting}
              />
            </div>

            <div>
              <Textarea
                label="Address *"
                placeholder="P.O. Box.."
                value={formData.address || ''}
                onChange={(e) => handleInputChange("address", e.target.value)}
                error={errors.address}
                disabled={loading || isSubmitting}
                className="min-h-20"
              />
            </div>
          </CardContent>
        </Card>

        {/* Support Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Support Information
            </CardTitle>
            <CardDescription>
              Configure support contact details for user assistance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Input
                  label="Support Contact *"
                  placeholder="Full Name"
                  value={formData.support_contact || ''}
                  onChange={(e) => handleInputChange("support_contact", e.target.value)}
                  error={errors.support_contact}
                  disabled={loading || isSubmitting}
                />
              </div>
              <div>
                <Input
                  label="Support Telephone *"
                  placeholder="+265 990 111 222"
                  value={formData.support_telephone || ''}
                  onChange={(e) => handleInputChange("support_telephone", e.target.value)}
                  error={errors.support_telephone}
                  disabled={loading || isSubmitting}
                />
              </div>
            </div>

            <div>
              <Input
                label="Support Email *"
                type="email"
                placeholder="email@..."
                value={formData.support_email || ''}
                onChange={(e) => handleInputChange("support_email", e.target.value)}
                error={errors.support_email}
                disabled={loading || isSubmitting}
              />
            </div>
          </CardContent>
        </Card>

        {/* System Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              System Settings
            </CardTitle>
            <CardDescription>
              Configure system behavior and default settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <label className="block text-sm font-medium mb-2">
                Landing Page * <span className="text-muted-foreground text-xs">(Only applies when user has access to the page)</span>
              </label>
              <Select
                value={formData.landing_page || landingPageOptions[0].value}
                onValueChange={(value) => handleInputChange("landing_page", value)}
                disabled={loading || isSubmitting}
              >
                <SelectTrigger className={errors.landing_page ? "border-destructive" : ""}>
                  <SelectValue placeholder="Select landing page" />
                </SelectTrigger>
                <SelectContent>
                  {landingPageOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.landing_page && (
                <p className="text-sm text-destructive mt-1">{errors.landing_page}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-6">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading || isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            loading={isSubmitting}
            disabled={loading}
            className="min-w-32"
          >
            Save Configuration
          </Button>
        </div>
      </form>
    </div>
  );
};
