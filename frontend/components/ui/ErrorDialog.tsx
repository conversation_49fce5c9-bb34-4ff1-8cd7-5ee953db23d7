import React from "react";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
    DialogDescription,
    DialogFooter,
} from "@/components/ui/dialog";
import { useError } from "@/context/ErrorContext";

export function ErrorDialog() {
    const { errorMessage, onRetry } = useError();

    if (!errorMessage) return null;

    return (
        <Dialog open={!!errorMessage}>
            <DialogContent className="md:max-w-xl animate-in fade-in duration-500">
                <DialogHeader>
                    <div className="flex flex-col items-center">
                        <svg
                            className="w-10 h-10 text-red-300 mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                            <path d="M12 8v4m0 4h.01" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" />
                        </svg>
                        <DialogTitle>Something went wrong</DialogTitle>
                    </div>
                </DialogHeader>
                <DialogDescription className="mb-4 text-center">
                    {errorMessage || "An unexpected error occurred. Please try again."}
                </DialogDescription>
                <div className="mb-4 text-xs text-center text-muted-foreground">
                    If the issue persists, contact support at{" "}
                    <a className="underline" href="mailto:<EMAIL>">
                        <EMAIL>
                    </a>
                </div>
                <DialogFooter>
                    <button
                        className="w-full px-4 py-2 rounded-lg bg-red-400 text-white hover:bg-red-500 transition"
                        onClick={onRetry}
                    >
                        Refresh
                    </button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
