"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>riangle, Trash2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface DeleteConfirmationDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onConfirm: () => void | Promise<void>;
    title?: string;
    description?: string;
    confirmationText: string;
    itemName?: string;
    loading?: boolean;
    variant?: "default" | "destructive";
}

export function DeleteConfirmationDialog({
    open,
    onOpenChange,
    onConfirm,
    title = "Confirm Deletion",
    description,
    confirmationText,
    itemName,
    loading = false,
    variant = "destructive",
}: DeleteConfirmationDialogProps) {
    const [inputValue, setInputValue] = useState("");
    const [isValid, setIsValid] = useState(false);

    // Reset input when dialog opens/closes
    useEffect(() => {
        if (!open) {
            setInputValue("");
            setIsValid(false);
        }
    }, [open]);

    // Validate input
    useEffect(() => {
        setIsValid(inputValue.trim() === confirmationText.trim());
    }, [inputValue, confirmationText]);

    const handleConfirm = async () => {
        if (!isValid || loading) return;

        try {
            await onConfirm();
            onOpenChange(false);
        } catch (error) {
            // Error handling is done by the parent component
            console.error("Delete confirmation error:", error);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && isValid && !loading) {
            handleConfirm();
        }
    };

    const defaultDescription = itemName
        ? `This action cannot be undone. This will permanently delete "${itemName}" and all associated data.`
        : "This action cannot be undone. This will permanently delete the selected item and all associated data.";

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <div className="flex items-center gap-3">
                        <div
                            className={`p-2 rounded-full ${
                                variant === "destructive" ? "bg-red-100 text-red-600" : "bg-orange-100 text-orange-600"
                            }`}
                        >
                            {variant === "destructive" ? (
                                <Trash2 className="h-5 w-5" />
                            ) : (
                                <AlertTriangle className="h-5 w-5" />
                            )}
                        </div>
                        <DialogTitle className="text-left">{title}</DialogTitle>
                    </div>
                    <DialogDescription className="text-left pt-2">
                        {description || defaultDescription}
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    <Alert variant={variant === "destructive" ? "destructive" : "default"}>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Warning:</strong> This action is irreversible. Please proceed with caution.
                        </AlertDescription>
                    </Alert>

                    <div className="space-y-2">
                        <Label htmlFor="confirmation-input">
                            To confirm, type{" "}
                            <code style={{ color: 'darkred' }} className="px-1.5 py-0.5 rounded text-sm font-mono">
                                {confirmationText}
                            </code>{" "}
                            below:
                        </Label>
                        <Input
                            autoComplete="off"
                            className={`${
                                inputValue && !isValid ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""
                            } ${isValid ? "border-green-300 focus:border-green-500 focus:ring-green-500" : ""}`}
                            disabled={loading}
                            id="confirmation-input"
                            placeholder={`Type "${confirmationText}" to confirm`}
                            spellCheck={false}
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            onKeyDown={handleKeyDown}
                        />
                        {inputValue && !isValid && (
                            <p className="text-sm text-red-600">
                                Text doesn&apos;t match. Please type exactly: {confirmationText}
                            </p>
                        )}
                    </div>
                </div>

                <DialogFooter className="flex-col sm:flex-row gap-2">
                    <Button
                        className="w-full sm:w-auto"
                        disabled={loading}
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                    >
                        Cancel
                    </Button>
                    <Button
                        className="w-full sm:w-auto"
                        disabled={!isValid || loading}
                        variant={variant}
                        onClick={handleConfirm}
                    >
                        {loading ? (
                            <>
                                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                Deleting...
                            </>
                        ) : (
                            <>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete {itemName ? `"${itemName}"` : "Item"}
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

// Hook for easier usage
export function useDeleteConfirmation() {
    const [isOpen, setIsOpen] = useState(false);
    const [deleteConfig, setDeleteConfig] = useState<{
        onConfirm: () => void | Promise<void>;
        confirmationText: string;
        itemName?: string;
        title?: string;
        description?: string;
        variant?: "default" | "destructive";
    } | null>(null);

    const openDeleteDialog = (config: {
        onConfirm: () => void | Promise<void>;
        confirmationText: string;
        itemName?: string;
        title?: string;
        description?: string;
        variant?: "default" | "destructive";
    }) => {
        setDeleteConfig(config);
        setIsOpen(true);
    };

    const closeDeleteDialog = () => {
        setIsOpen(false);
        setDeleteConfig(null);
    };

    return {
        isOpen,
        openDeleteDialog,
        closeDeleteDialog,
        deleteConfig
    };
}
