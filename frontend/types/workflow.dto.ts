import { Pagination } from "./common.type";

// Enums
export type ActionMode = "BEFORE" | "AFTER";

export type WorkflowTemplateCode = "ORGANIZATION_REGISTRATION" | "LICENCE_RENEWAL" | "PERMIT_APPLICATION";

// Base DTOs matching backend structure
export interface TemplateStageRoleDto {
    id: string;
    template_stage_id: string;
    role_id: string;
    is_active: boolean;
    role?: {
        id: string;
        name: string;
        description: string;
    } | null;
}

export interface TemplateStageTriggerDto {
    id: string;
    template_stage_id: string;
    trigger_id: string;
    action_mode: ActionMode;
    is_active: boolean;
    trigger?: string | null;
}

export interface TemplateStageDto {
    id: string;
    template_id: string;
    name: string;
    description?: string | null;
    is_active: boolean;
    position: number;
    triggers: TemplateStageTriggerDto[];
    roles: TemplateStageRoleDto[];
}

export interface TemplateDto {
    id: string;
    name: string;
    code: string;
    description?: string | null;
    is_active: boolean;
    stages: TemplateStageDto[];
}

export interface WorkflowStageDto {
    id: string;
    approver?: {
        user_id: string;
        first_name: string;
        last_name: string;
        email: string;
    } | null;
    created_at: string;
}

export interface WorkflowDto {
    id: string;
    template?: TemplateDto | null;
    next_stage?: TemplateStageDto | null;
    approvals: WorkflowStageDto[];
}

export interface TemplateStageTriggerRequest {
    trigger_id: string;
    action_mode: ActionMode;
}

export interface TemplateStageRequest {
    name: string;
    description?: string;
    position: number;
    is_active?: boolean;
    roles?: string[];
    triggers?: TemplateStageTriggerRequest[];
}

export interface TemplateRequest {
    name: string;
    code: string;
    description?: string;
    is_active?: boolean;
    stages?: TemplateStageRequest[];
}

// Filter DTOs
export interface TemplateFilter extends Partial<Pagination> {
    name?: string;
    code?: string;
    is_active?: boolean;
}

export interface TemplateStageFilter extends Partial<Pagination> {
    name?: string;
    is_active?: boolean;
}

export interface TemplateStageRoleFilter extends Partial<Pagination> {
    role_id?: string;
    is_active?: boolean;
}

export interface TemplateStageTriggerFilter extends Partial<Pagination> {
    trigger_id?: string;
    action_mode?: ActionMode;
    is_active?: boolean;
}

// UI-specific types
export interface WorkflowTemplateOption {
    code: WorkflowTemplateCode;
    name: string;
    description: string;
    icon: string;
}

export interface StageFormData {
    name: string;
    description?: string;
    position: number;
    selectedRoles: string[];
    selectedTriggers: Array<{
        trigger_id: string;
        action_mode: ActionMode;
    }>;
}

// API Response types
export interface TemplatesResponse {
    data: TemplateDto[];
    total: number;
    page: number;
    size: number;
    pages: number;
}

export interface StagesResponse {
    data: TemplateStageDto[];
    total: number;
    page: number;
    size: number;
    pages: number;
}
